import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { childTheme } from '../../utils/themes';
import { childApi } from '../../api/apiService';
import { format } from 'date-fns';
import RichTextRenderer from '../../components/common/RichTextRenderer';

// 不再使用mock数据，只使用真实API数据

// 不再使用mock数据，所有过期任务数据从API获取

// 任务状态转换函数
const convertTaskStatus = (statusFromBackend) => {
  // 直接返回后端的大写状态
  return statusFromBackend;
};

// 根据状态返回对应的中文标签
const getStatusLabel = (status) => {
  if (!status) return '未知';

  switch(status.toUpperCase()) {
    case 'NOT_STARTED': return '未开始';
    case 'IN_PROGRESS': return '进行中';
    case 'PENDING': return '待审批';
    case 'COMPLETED': return '已完成';
    case 'REJECTED': return '已作废'; // 审批拒绝，质量不过关
    case 'OVERDUE': return '已过期'; // 任务过期，没有完成
    default: return status;
  }
};

// 任务状态排序优先级
const getStatusPriority = (status) => {
  if (!status) return 999;

  switch(status.toUpperCase()) {
    case 'NOT_STARTED': return 1;  // 未开始
    case 'IN_PROGRESS': return 2;  // 进行中
    case 'PENDING': return 3;      // 待审核
    case 'REJECTED': return 4;     // 已作废
    case 'COMPLETED': return 5;    // 已完成
    case 'OVERDUE': return 6;      // 已过期
    default: return 999;
  }
};

// 任务排序函数
const sortTasks = (tasks) => {
  return tasks.sort((a, b) => {
    const priorityA = getStatusPriority(a.status);
    const priorityB = getStatusPriority(b.status);

    // 首先按状态优先级排序
    if (priorityA !== priorityB) {
      return priorityA - priorityB;
    }

    // 状态相同时，按任务ID排序（保持稳定排序）
    return a.id - b.id;
  });
};

const TaskBoard = ({ onPointsChange, currentPoints: initialPoints, dailyPointsChange: initialDailyChange, shouldRefresh, onRefreshed }) => {
  const navigate = useNavigate();
  const [tasks, setTasks] = useState([]);
  const [yesterdayTasks, setYesterdayTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTask, setActiveTask] = useState(null);
  const [activeTab, setActiveTab] = useState('today'); // 'today' 或 'yesterday'
  const [currentPoints, setCurrentPoints] = useState(initialPoints);
  const [dailyPointsChange, setDailyPointsChange] = useState(initialDailyChange);

  // 格式化时长显示（分钟）
  const formatDuration = (minutes) => {
    if (!minutes || minutes <= 0) {
      return '1分钟'; // 不足1分钟按1分钟计算
    }
    return Math.ceil(minutes) + '分钟'; // 向上取整
  };

  // 获取最新积分数据
  const fetchPointsData = async () => {
    try {
      console.log('TaskBoard: 正在获取最新积分数据...');
      // 获取总积分
      const pointsResponse = await childApi.getPointBalance();
      const points = pointsResponse.data.totalPoints;
      console.log('TaskBoard: 获取到的积分:', points);
      setCurrentPoints(points);

      // 获取今日积分变化
      const todayResponse = await childApi.getTodayPointChanges();
      const todayPoints = todayResponse.data.reduce((total, record) => {
        return total + record.pointChange;
      }, 0);
      console.log('TaskBoard: 获取到的今日积分变化:', todayPoints);
      setDailyPointsChange(todayPoints);

      // 如果从父组件获取到的积分与API不同，通知父组件更新
      if (points !== initialPoints && onPointsChange) {
        onPointsChange(points - initialPoints);
      }
    } catch (error) {
      console.error('TaskBoard: 获取积分数据失败:', error);
      // 失败时不更新积分，保留现有值
    }
  };

  // 从API获取任务列表
  const fetchTasks = async () => {
    setLoading(true);
    try {
      // 分别获取今日任务和昨日任务
      const todayResponse = await childApi.getTodayTasks();
      const yesterdayResponse = await childApi.getYesterdayTasks();
      
      const formatTask = (task) => {
        let formattedDueTime = '18:00'; // 默认值
        if (task.dueTime) {
          const timeDate = new Date(`2023-01-01T${task.dueTime}`);
          if (!isNaN(timeDate.getTime())) {
            formattedDueTime = format(timeDate, 'HH:mm');
          } else {
            console.warn(`后端返回了无效的dueTime: ${task.dueTime} (任务ID: ${task.id})`);
          }
        }
        
        return {
          id: task.id,
          title: task.title,
          description: task.description,
          expectedMinutes: task.expectedMinutes,
          basePoints: task.basePoints,
          dueTime: formattedDueTime,
          status: convertTaskStatus(task.status),
          startTime: task.startTime,
          endTime: task.endTime,
          taskType: task.taskType,
          dueDate: task.dueDate,
          actualMinutes: task.actualMinutes || 0
        };
      };

      // 格式化今日任务
      const formattedTodayTasks = (todayResponse.data || []).map(formatTask);

      // 格式化昨日任务 - 显示昨天的所有任务
      const formattedYesterdayTasks = (yesterdayResponse.data || []).map(formatTask);

      // 对任务进行排序
      const sortedTodayTasks = sortTasks(formattedTodayTasks);
      const sortedYesterdayTasks = sortTasks(formattedYesterdayTasks);

      setTasks(sortedTodayTasks);
      setYesterdayTasks(sortedYesterdayTasks);
      
      // 如果是由shouldRefresh触发的刷新，调用onRefreshed回调
      if (shouldRefresh && onRefreshed) {
        onRefreshed();
      }
    } catch (error) {
      console.error('获取任务失败:', error);
      // 如果API请求失败，保留现有数据，不设置为空数组
    } finally {
      setLoading(false);
    }
  };

  // 初始加载和定时刷新
  useEffect(() => {
    // 首次加载
    fetchTasks();
    fetchPointsData(); // 获取最新积分数据
    
    // 页面可见性变化时刷新
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        fetchTasks();
        fetchPointsData();
      }
    };
    
    // 添加可见性变化监听
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // 清理函数
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // 监听shouldRefresh变化
  useEffect(() => {
    if (shouldRefresh) {
      fetchTasks();
    }
  }, [shouldRefresh]);

  const handleViewDetails = (task) => {
    navigate(`/child/task/${task.id}`);
  };

  // 修改handleStartTask函数支持与API交互
  const handleStartTask = async (e, taskId) => {
    e.stopPropagation();
    try {
      // 调用API开始任务
      await childApi.startTask(taskId);
      
      // 查找任务，可能在今日任务或昨日任务中
      let taskToStart = tasks.find(t => t.id === taskId);
      let isFromYesterdayTasks = false;

      if (!taskToStart) {
        taskToStart = yesterdayTasks.find(t => t.id === taskId);
        isFromYesterdayTasks = true;
      }
      
      if (!taskToStart) return;
      
      // 更新任务状态
      const updatedTask = {
        ...taskToStart,
        status: 'IN_PROGRESS',
        startTime: new Date().toISOString()
      };
      
      if (isFromYesterdayTasks) {
        // 从昨日任务移到今日任务
        setYesterdayTasks(prev => sortTasks(prev.filter(t => t.id !== taskId)));
        setTasks(prev => sortTasks([...prev, updatedTask]));
      } else {
        // 更新今日任务
        setTasks(prev => sortTasks(prev.map(t => t.id === taskId ? updatedTask : t)));
      }
      
      setActiveTask(taskId);
      setActiveTab('today'); // 切换到今日任务视图
    } catch (error) {
      console.error('开始任务失败:', error);
      alert('开始任务失败，请稍后再试');
    }
  };

  // 完成任务
  const handleCompleteTask = async (e, taskId) => {
    e.stopPropagation();
    try {
      // 调用API完成任务
      await childApi.completeTask(taskId);
      
      // 更新本地状态
      setTasks(prev => sortTasks(prev.map(task => {
        if (task.id === taskId) {
          return {
            ...task,
            status: 'PENDING',
            endTime: new Date().toISOString()
          };
        }
        return task;
      })));
      
      setActiveTask(null);
      
      // 刷新积分数据
      setTimeout(async () => {
        try {
          // 先记录完成前的积分
          const oldPoints = currentPoints;
          
          // 获取最新积分余额
          const pointResponse = await childApi.getPointBalance();
          const newPoints = pointResponse.data.totalPoints;
          console.log('完成任务后获取到的积分:', newPoints, '完成前积分:', oldPoints);
          
          // 计算积分变化
          const pointsDiff = newPoints - oldPoints;
          
          if (pointsDiff !== 0) {
            console.log(`检测到积分变化: ${pointsDiff}`);
            
            // 更新本地显示的积分
            setCurrentPoints(newPoints);
            
            // 更新今日积分变化 - 获取最新今日积分变化
            try {
              const todayResponse = await childApi.getTodayPointChanges();
              const todayPoints = todayResponse.data.reduce((total, record) => {
                return total + record.pointChange;
              }, 0);
              console.log(`获取到的今日积分变化: ${todayPoints}`);
              setDailyPointsChange(todayPoints);
            } catch (err) {
              // 如果获取失败，则使用计算方式更新
              console.error('获取今日积分变化失败，使用计算方式更新:', err);
              setDailyPointsChange(prev => prev + pointsDiff);
            }
            
            // 触发积分变化动画
            if (onPointsChange) {
              onPointsChange(pointsDiff);
            }
            
            // 显示完成动画
            showCompletionAnimation(taskId, pointsDiff);
          }
        } catch (error) {
          console.error('获取积分数据失败:', error);
          // 失败时仍尝试刷新积分
          fetchPointsData();
        }
      }, 1000);
    } catch (error) {
      console.error('完成任务失败:', error);
      alert('完成任务失败，请稍后再试');
    }
  };

  // 显示完成动画
  const showCompletionAnimation = (taskId, points) => {
    // 实际项目中可以添加更炫酷的完成动画
    console.log(`Task ${taskId} completed! Earned ${points} points.`);
  };

  if (loading) {
    return <LoadingContainer>加载任务中...</LoadingContainer>;
  }

  return (
    <TaskBoardContainer
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <Header>
        <TabsContainer>
          <Tab
            active={activeTab === 'today'}
            onClick={() => setActiveTab('today')}
          >
            <TabIcon>📝</TabIcon>
            今日任务
            {tasks.length > 0 && <TabBadge>{tasks.length}</TabBadge>}
          </Tab>
          <Tab
            active={activeTab === 'yesterday'}
            onClick={() => setActiveTab('yesterday')}
          >
            <TabIcon>📅</TabIcon>
            昨日任务
            {yesterdayTasks.length > 0 && <TabBadge>{yesterdayTasks.length}</TabBadge>}
          </Tab>
        </TabsContainer>
      </Header>
      
      <TaskList>
        {activeTab === 'today' && (
          <AnimatePresence mode="sync">
            {tasks.map(task => (
              <TaskCard
                key={task.id}
                layoutId={`task-${task.id}`}
                status={task.status}
                taskType={task.taskType}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ type: 'spring', stiffness: 300, damping: 25 }}
                onClick={() => handleViewDetails(task)}
              >
                <TaskHeader>
                  <TaskTitle>{task.title}</TaskTitle>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    flexShrink: 0
                  }}>
                    {task.basePoints > 0 && (
                      <TaskPoints style={{ margin: 0 }}>
                        <StarIcon>⭐</StarIcon>
                        {task.basePoints}
                      </TaskPoints>
                    )}
                    <StatusBadge $status={task.status}>
                      {getStatusLabel(task.status)}
                    </StatusBadge>
                  </div>
                </TaskHeader>

                <TaskDetails>
                  <TaskDetail>
                    <DetailIcon>⏱️</DetailIcon>
                    预计耗时：{formatDuration(task.expectedMinutes)}
                  </TaskDetail>
                  <TaskDetail>
                    <DetailIcon>⏰</DetailIcon>
                    实际耗时：{task.actualMinutes ? (
                      <span style={{
                        color: task.actualMinutes > task.expectedMinutes ? '#ff4757' : 'inherit',
                        fontWeight: task.actualMinutes > task.expectedMinutes ? 'bold' : 'normal'
                      }}>
                        {formatDuration(task.actualMinutes)}
                      </span>
                    ) : '-'}
                  </TaskDetail>
                </TaskDetails>
              </TaskCard>
            ))}
          </AnimatePresence>
        )}
        
        {activeTab === 'yesterday' && (
          <AnimatePresence mode="sync">
            {yesterdayTasks.length > 0 ? (
              yesterdayTasks.map(task => (
                <TaskCard
                  key={task.id}
                  layoutId={`task-${task.id}`}
                  status={task.status}
                  taskType={task.taskType}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ type: 'spring', stiffness: 300, damping: 25 }}
                  onClick={() => handleViewDetails(task)}
                >
                  <TaskHeader>
                    <TaskTitle>{task.title}</TaskTitle>
                    <TaskHeaderRight>
                      <TaskPoints>
                        <StarIcon>⭐</StarIcon>
                        {task.basePoints}
                      </TaskPoints>
                      <StatusBadge $status={task.status}>
                        {getStatusLabel(task.status)}
                      </StatusBadge>
                    </TaskHeaderRight>
                  </TaskHeader>
                  
                  <TaskDetails>
                    <TaskDetail>
                      <DetailIcon>⏱️</DetailIcon>
                      预计耗时：{formatDuration(task.expectedMinutes)}
                    </TaskDetail>
                    <TaskDetail>
                      <DetailIcon>⏰</DetailIcon>
                      实际耗时：<span style={{
                        color: task.actualMinutes > task.expectedMinutes ? '#ff4757' : 'inherit',
                        fontWeight: task.actualMinutes > task.expectedMinutes ? 'bold' : 'normal'
                      }}>
                        {formatDuration(task.actualMinutes)}
                      </span>
                    </TaskDetail>
                  </TaskDetails>
                  
                  <TaskActions>
                    <ActionButton
                      onClick={(e) => handleStartTask(e, task.id)}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      primary
                    >
                      开始补做
                    </ActionButton>
                  </TaskActions>
                </TaskCard>
              ))
            ) : (
              <EmptyStateContainer>
                <EmptyStateIcon>🎉</EmptyStateIcon>
                <EmptyStateText>没有昨日任务</EmptyStateText>
              </EmptyStateContainer>
            )}
          </AnimatePresence>
        )}
      </TaskList>


    </TaskBoardContainer>
  );
};

const TaskBoardContainer = styled(motion.div)`
  padding: 1.5rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f5f7fa 0%, #ebf0f6 100%);
  border-radius: ${childTheme.borderRadius};
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  
  &::before {
    content: '';
    position: absolute;
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
    opacity: 0.6;
    z-index: 0;
  }
  
  &::after {
    content: '';
    position: absolute;
    bottom: -50px;
    left: -50px;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255,204,0,0.3) 0%, rgba(255,204,0,0) 70%);
    z-index: 0;
  }
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
`;

const TabsContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin: 0 auto;
`;

const Tab = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== 'active',
})`
  padding: 0.6rem 1.5rem;
  background: ${props => props.active
    ? 'linear-gradient(135deg, #42a5f5, #1976d2)'
    : 'white'};
  color: ${props => props.active ? 'white' : '#666'};
  border-radius: 25px;
  font-weight: 600;
  display: flex;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
`;

const TabIcon = styled.span`
  font-size: 1.2rem;
  margin-right: 0.5rem;
`;

const TabBadge = styled.span`
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  color: #1976d2;
  min-width: 18px;
  height: 18px;
  border-radius: 9px;
  font-size: 0.7rem;
  font-weight: bold;
  margin-left: 0.5rem;
  padding: 0 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
`;

const TaskList = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  overflow-y: auto;
  padding: 0.5rem;
  min-height: 300px;
  position: relative;
`;

const TaskCard = styled(motion.div)`
  background: white;
  border-radius: ${childTheme.borderRadius};
  padding: 1.5rem;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 120px;
  max-height: 200px;
  border-left: 5px solid ${props => {
    const typeColor = (props.taskType && props.taskType.toUpperCase() === 'REQUIRED') ? '#ff3b30' : '#ffcc00';
    switch (props.status) {
      case 'COMPLETED':
      case 'PENDING':
        return childTheme.statusColors.COMPLETED;
      case 'IN_PROGRESS':
        return childTheme.statusColors.IN_PROGRESS;
      case 'OVERDUE':
        return childTheme.statusColors.OVERDUE;
      case 'REJECTED':
        return childTheme.statusColors.REJECTED;
      default:
        return typeColor;
    }
  }};
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  z-index: 2;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.12);
    cursor: pointer;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.4) 100%);
    z-index: -1;
  }
`;

const TaskHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
`;

const TaskTitle = styled.h3`
  font-size: 1.4rem;
  font-weight: 700;
  margin: 0;
  color: ${childTheme.textColor};
  letter-spacing: 0.02em;
  flex: 1;
  display: flex;
  align-items: center;
  line-height: 1.3;
  word-break: break-word;
`;

const TaskFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 1rem;
`;

const TaskTime = styled.div`
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: #888;
`;

const TimeIcon = styled.span`
  margin-right: 0.5rem;
`;

const TaskHeaderRight = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
`;

const TaskPoints = styled.div`
  display: flex;
  align-items: center;
  font-weight: 800;
  font-size: 1.4rem;
  padding: 5px 12px;
  background: transparent;
  color: ${childTheme.primaryColor};
  border-radius: 20px;
  text-shadow: none;
  position: relative;
  overflow: hidden;
`;

const StatusBadge = styled.div`
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  background-color: ${props => {
    switch (props.$status.toUpperCase()) {
      case 'COMPLETED':
        return childTheme.statusColors.COMPLETED;
      case 'PENDING':
        return childTheme.statusColors.COMPLETED;
      case 'IN_PROGRESS':
        return childTheme.statusColors.IN_PROGRESS;
      case 'REJECTED':
        return childTheme.statusColors.REJECTED;
      case 'OVERDUE':
        return childTheme.statusColors.OVERDUE;
      default: // NOT_STARTED
        return 'rgba(0, 0, 0, 0.1)';
    }
  }};
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  flex-shrink: 0;
`;

const TaskDetails = styled.div`
  margin-bottom: 1rem;
`;

const TaskDetail = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 0.4rem;
  color: #666;
  font-size: 0.95rem;
  padding: 0.5rem;
  border-radius: 0.5rem;
  background-color: rgba(0, 0, 0, 0.02);
`;

const DetailIcon = styled.span`
  margin-right: 0.75rem;
  font-size: 1.1rem;
`;

const TaskActions = styled.div`
  display: flex;
  justify-content: center;
  margin-top: auto;
`;

const ActionButton = styled(motion.button)`
  padding: 0.85rem 1.8rem;
  border-radius: ${childTheme.borderRadius};
  border: none;
  font-weight: 600;
  font-size: 1.05rem;
  cursor: pointer;
  background: ${props => props.primary ? childTheme.gradients.primary : 
              props.success ? childTheme.gradients.success : 
              childTheme.gradients.secondary};
  color: white;
  box-shadow: 0 8px 15px ${props => props.primary ? 'rgba(0, 122, 255, 0.3)' :
                            props.success ? 'rgba(76, 217, 100, 0.3)' : 
                            'rgba(255, 45, 85, 0.3)'};
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 20px ${props => props.primary ? 'rgba(0, 122, 255, 0.4)' :
                              props.success ? 'rgba(76, 217, 100, 0.4)' : 
                              'rgba(255, 45, 85, 0.4)'};
  }
  
  &:focus {
    outline: none;
  }
`;

const PendingIndicator = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  color: ${childTheme.statusColors.COMPLETED};
  font-weight: 500;
`;

const LoadingDots = styled.div`
  display: flex;
  margin-bottom: 0.5rem;
`;

const Dot = styled.div`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: ${childTheme.statusColors.COMPLETED};
  margin: 0 3px;
  animation: bounce 1.4s infinite ease-in-out;
  animation-delay: ${props => props.delay}s;
  
  @keyframes bounce {
    0%, 100% {
      transform: scale(0);
    }
    50% {
      transform: scale(1);
    }
  }
`;

const CompletedIndicator = styled.div`
  display: flex;
  align-items: center;
  color: ${childTheme.statusColors.COMPLETED};
  font-weight: 600;
`;

const CheckIcon = styled.span`
  font-size: 1.2rem;
  margin-right: 0.5rem;
`;

const RejectedIndicator = styled.div`
  display: flex;
  align-items: center;
  color: ${childTheme.statusColors.REJECTED};
  font-weight: 600;
`;

const CloseIcon = styled.span`
  font-size: 1.2rem;
  margin-right: 0.5rem;
`;

const StatusIndicator = styled.div`
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 40px 40px 0;
  border-color: transparent ${props => {
    const typeColor = (props.taskType && props.taskType.toUpperCase() === 'REQUIRED') ? '#ff3b30' : '#ffcc00';
    switch (props.status) {
      case 'COMPLETED':
      case 'PENDING':
        return childTheme.statusColors.COMPLETED;
      case 'IN_PROGRESS':
        return childTheme.statusColors.IN_PROGRESS;
      case 'OVERDUE':
        return childTheme.statusColors.OVERDUE;
      case 'REJECTED':
        return childTheme.statusColors.REJECTED;
      default:
        return typeColor;
    }
  }} transparent transparent;
`;

const EmptyStateContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  grid-column: 1 / -1;
`;

const EmptyStateIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
  animation: bounce 2s infinite alternate;
  
  @keyframes bounce {
    from { transform: translateY(0); }
    to { transform: translateY(-10px); }
  }
`;

const EmptyStateText = styled.div`
  color: #666;
  font-size: 1.1rem;
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 1.2rem;
  color: ${childTheme.textColor};
`;

const StarIcon = styled.span`
  color: #ffd700;
  font-size: 1.2rem;
  animation: pulse 2s infinite;
  
  @keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
  }
`;



const RequiredBadge = styled.span`
  background-color: ${childTheme.primaryColor};
  color: white;
  padding: 0.2rem 0.6rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-right: 0.5rem;
`;

export default TaskBoard; 