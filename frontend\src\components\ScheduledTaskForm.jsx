import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import RichTextEditor from './RichTextEditor';

const FormContainer = styled.form`
    background-color: white;
    padding: 2rem;
    border-radius: 12px;
    max-width: 800px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
`;

const FormTitle = styled.h2`
    margin-top: 0;
    margin-bottom: 1.5rem;
    color: #333;
    font-size: 1.5rem;
`;

const FormDescription = styled.p`
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
`;

const FormGroup = styled.div`
    margin-bottom: 1rem;
`;

const Label = styled.label`
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
`;

const Input = styled.input`
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
`;

const Select = styled.select`
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    background-color: white;
`;

const WeekdayButton = styled.button`
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    padding: 0.5rem 0.8rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: ${props => props.selected ? '#4a90e2' : 'white'};
    color: ${props => props.selected ? 'white' : '#333'};
    cursor: pointer;
    transition: all 0.2s;
    
    &:hover {
        background-color: ${props => props.selected ? '#3a80d2' : '#f0f0f0'};
    }
`;

const SubmitButton = styled.button`
    margin-top: 1rem;
    padding: 0.75rem 1.5rem;
    background-color: #4a90e2;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    
    &:hover {
        background-color: #3a80d2;
    }
`;

const CancelButton = styled.button`
    margin-top: 1rem;
    margin-right: 1rem;
    padding: 0.75rem 1.5rem;
    background-color: #f1f2f6;
    color: #333;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    
    &:hover {
        background-color: #dfe4ea;
    }
`;

const ButtonContainer = styled.div`
    display: flex;
    justify-content: flex-end;
`;

const InfoText = styled.div`
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.25rem;
    margin-bottom: 0.75rem;
    background-color: #f9f9f9;
    padding: 0.5rem;
    border-radius: 4px;
    border-left: 3px solid ${props => props.type === 'REWARD' ? '#2ed573' : '#ff4757'};
`;

const weekDays = [
    { label: '一', value: 'MON' },
    { label: '二', value: 'TUE' },
    { label: '三', value: 'WED' },
    { label: '四', value: 'THU' },
    { label: '五', value: 'FRI' },
    { label: '六', value: 'SAT' },
    { label: '日', value: 'SUN' },
];

const ScheduledTaskForm = ({ onSubmit, initialData = null, onCancel }) => {
    const [title, setTitle] = useState('');
    const [description, setDescription] = useState('');
    const [points, setPoints] = useState(1);
    const [type, setType] = useState('REWARD');
    const [taskRequirementType, setTaskRequirementType] = useState('REQUIRED'); // 新增：任务类型（必做/选做）
    const [repeatType, setRepeatType] = useState('daily'); // 'daily' or 'weekly'
    const [selectedDays, setSelectedDays] = useState([]);
    const [time, setTime] = useState('09:00');
    const [expectedMinutes, setExpectedMinutes] = useState(30);
    const [directToReview, setDirectToReview] = useState(false);
    const [active, setActive] = useState(true);

    // 当初始数据变化时，更新表单
    useEffect(() => {
        if (initialData) {
            console.log('收到编辑任务数据:', initialData);
            console.log('dueTime类型:', typeof initialData.dueTime, '值:', initialData.dueTime);
            
            setTitle(initialData.title || '');
            setDescription(initialData.description || '');
            setPoints(initialData.points || 1);
            setType(initialData.type || 'REWARD');
            setTaskRequirementType(initialData.taskRequirementType || 'REQUIRED'); // 新增：设置任务类型
            setExpectedMinutes(initialData.expectedMinutes || 30);
            setDirectToReview(initialData.directToReview || false);
            setActive(initialData.active !== undefined ? initialData.active : true);
            
            // 处理时间
            if (initialData.dueTime) {
                try {
                    // 检查dueTime的类型
                    const dueTimeStr = typeof initialData.dueTime === 'string' 
                        ? initialData.dueTime 
                        : initialData.dueTime.toString();
                    
                    // 提取时间部分（HH:MM）
                    const timeStr = dueTimeStr.includes(':') 
                        ? dueTimeStr.substring(0, 5) 
                        : '09:00'; // 默认值
                    
                    setTime(timeStr);
                } catch (err) {
                    console.error('处理dueTime时出错:', err);
                    setTime('09:00'); // 出错时使用默认值
                }
            }
            
            // 处理CRON表达式
            if (initialData.cronExpression) {
                try {
                    const cronParts = initialData.cronExpression.split(' ');
                    if (cronParts.length >= 6) {
                        // 设置时间
                        const hour = cronParts[2];
                        const minute = cronParts[1];
                        
                        // 确保hour和minute是有效的字符串
                        const validHour = typeof hour === 'string' ? hour : '09';
                        const validMinute = typeof minute === 'string' ? minute : '00';
                        
                        setTime(`${validHour.padStart(2, '0')}:${validMinute.padStart(2, '0')}`);
                        
                        // 判断重复类型
                        if (cronParts[5] === '?') {
                            // 每天
                            setRepeatType('daily');
                        } else {
                            // 每周
                            setRepeatType('weekly');
                            try {
                                const days = cronParts[5].split(',');
                                setSelectedDays(days);
                            } catch (err) {
                                console.error('处理周天数据时出错:', err);
                                setSelectedDays([]);
                            }
                        }
                    }
                } catch (err) {
                    console.error('处理CRON表达式时出错:', err);
                    // 使用默认值
                    setTime('09:00');
                    setRepeatType('daily');
                    setSelectedDays([]);
                }
            }
        }
    }, [initialData]);

    // Cron Expression Generation Logic
    const generateCron = () => {
        const [hour, minute] = time.split(':');
        if (repeatType === 'daily') {
            return `0 ${minute} ${hour} * * ?`;
        }
        if (repeatType === 'weekly' && selectedDays.length > 0) {
            return `0 ${minute} ${hour} ? * ${selectedDays.join(',')}`;
        }
        return ''; // Return empty if weekly is selected but no days are chosen
    };
    
    const handleDayToggle = (dayValue) => {
        setSelectedDays(prev => 
            prev.includes(dayValue) 
                ? prev.filter(d => d !== dayValue) 
                : [...prev, dayValue]
        );
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        const cronExpression = generateCron();
        if (!cronExpression) {
            alert('请至少选择一个星期内的日期！');
            return;
        }
        
        // 将时间字符串转换为LocalTime格式
        const [hours, minutes] = time.split(':');
        const timeString = `${hours}:${minutes}:00`;
        
        onSubmit({
            title,
            description,
            points: Number(points),
            type,
            taskRequirementType, // 新增：任务类型
            cronExpression,
            dueTime: timeString, // 使用正确格式的时间
            expectedMinutes,
            active,
            directToReview
        });

        // 重置表单
        setTitle('');
        setDescription('');
        setPoints(10);
        setType('REWARD');
        setTaskRequirementType('REQUIRED'); // 新增：重置任务类型
        setRepeatType('daily');
        setSelectedDays([]);
        setTime('09:00');
        setExpectedMinutes(30);
        setDirectToReview(false);
    };

    return (
        <FormContainer onSubmit={handleSubmit}>
            <FormTitle>{initialData ? '编辑计划' : '创建计划'}</FormTitle>
            <FormDescription>
                {initialData 
                    ? '修改现有的计划或惩罚项目。' 
                    : '创建定期执行的任务或惩罚项目，所有任务和惩罚都需要家长审批。'}
            </FormDescription>
            
            <FormGroup>
                <Label>标题:</Label>
                <Input 
                    type="text" 
                    value={title} 
                    onChange={e => setTitle(e.target.value)} 
                    placeholder="例如：每日阅读" 
                    required 
                />
            </FormGroup>
            
            <FormGroup>
                <Label>描述:</Label>
                <RichTextEditor
                    value={description}
                    onChange={setDescription}
                    placeholder="例如：阅读30分钟，完成后需家长审核。支持粘贴图片！"
                />
            </FormGroup>

            <FormGroup>
                <Label>类型:</Label>
                <Select value={type} onChange={e => setType(e.target.value)}>
                    <option value="REWARD">奖励审批</option>
                    <option value="PENALTY_APPROVAL">惩罚审批</option>
                </Select>
                <InfoText type={type}>
                    {type === 'REWARD' 
                        ? '奖励审批：任务完成后，需要家长审核确认，通过后才会加分。' 
                        : '惩罚审批：当触发条件发生时（如晚归），需要家长审核，确认后会扣分。'}
                </InfoText>
            </FormGroup>
            
            <FormGroup>
                <Label>分值:</Label>
                <Input
                    type="number"
                    value={points}
                    onChange={e => setPoints(e.target.value)}
                    min="1"
                    required
                />
                <InfoText type={type}>
                    {type === 'REWARD'
                        ? '完成任务后审批通过将获得的积分数量'
                        : '触发惩罚后审批通过将扣除的积分数量'}
                </InfoText>
            </FormGroup>

            <FormGroup>
                <Label>任务类型:</Label>
                <Select value={taskRequirementType} onChange={e => setTaskRequirementType(e.target.value)}>
                    <option value="REQUIRED">必做任务</option>
                    <option value="OPTIONAL">选做任务</option>
                </Select>
                <InfoText type={taskRequirementType}>
                    {taskRequirementType === 'REQUIRED'
                        ? '必做任务：未完成将会有惩罚扣分'
                        : '选做任务：完成有奖励，未完成不扣分'}
                </InfoText>
            </FormGroup>

            <FormGroup>
                <Label>重复类型:</Label>
                <Select value={repeatType} onChange={e => setRepeatType(e.target.value)}>
                    <option value="daily">每天</option>
                    <option value="weekly">每周</option>
                </Select>
            </FormGroup>
            
            {repeatType === 'weekly' && (
                <FormGroup>
                    <Label>选择星期:</Label>
                    <div>
                        {weekDays.map(day => (
                            <WeekdayButton 
                                type="button" 
                                key={day.value}
                                onClick={() => handleDayToggle(day.value)}
                                selected={selectedDays.includes(day.value)}
                            >
                                {day.label}
                            </WeekdayButton>
                        ))}
                    </div>
                </FormGroup>
            )}
            
            <FormGroup>
                <Label>时间:</Label>
                <Input 
                    type="time" 
                    value={time} 
                    onChange={e => setTime(e.target.value)} 
                    required 
                />
                <InfoText>
                    {type === 'REWARD' 
                        ? '任务将在指定时间生成，等待孩子完成' 
                        : '惩罚条件将在指定时间开始生效'}
                </InfoText>
            </FormGroup>

            <FormGroup>
                <Label>预计用时(分钟):</Label>
                <Input
                    type="number"
                    value={expectedMinutes}
                    onChange={e => setExpectedMinutes(Number(e.target.value))}
                    min="1"
                    required
                />
                <InfoText>
                    预计完成该任务需要的时间（分钟）
                </InfoText>
            </FormGroup>

            <FormGroup>
                <Label>
                    <input
                        type="checkbox"
                        checked={active}
                        onChange={e => setActive(e.target.checked)}
                        style={{ marginRight: '8px' }}
                    />
                    启用计划
                </Label>
                <InfoText type={active ? 'REWARD' : 'PENALTY'}>
                    {active
                        ? '✓ 计划已启用，将按照设定的时间自动生成任务'
                        : '○ 计划已暂停，不会生成新任务（可随时重新启用）'
                    }
                </InfoText>
            </FormGroup>

            <FormGroup>
                <Label>
                    <input
                        type="checkbox"
                        checked={directToReview}
                        onChange={e => setDirectToReview(e.target.checked)}
                        style={{ marginRight: '8px' }}
                    />
                    直接进入审核
                </Label>
                <InfoText type={directToReview ? 'PENALTY' : 'REWARD'}>
                    {directToReview
                        ? '✓ 生成的任务将直接进入审核列表，需要家长审批（适用于监督类任务，如"9点半前回家"）'
                        : '○ 生成的任务将进入任务池，小孩可以自己操作完成（适用于日常任务，如"阅读30分钟"）'
                    }
                </InfoText>
            </FormGroup>

            <ButtonContainer>
                {onCancel && (
                    <CancelButton type="button" onClick={onCancel}>
                        取消
                    </CancelButton>
                )}
                <SubmitButton type="submit">
                    {initialData ? '更新计划' : '保存计划'}
                </SubmitButton>
            </ButtonContainer>
        </FormContainer>
    );
};

export default ScheduledTaskForm; 