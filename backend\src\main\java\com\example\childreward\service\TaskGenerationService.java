package com.example.childreward.service;

import com.example.childreward.entity.ScheduledTask;
import com.example.childreward.entity.Task;
import com.example.childreward.entity.TaskRequirementType;
import com.example.childreward.entity.TaskStatus;
import com.example.childreward.entity.TaskType;
import com.example.childreward.repository.ScheduledTaskRepository;
import com.example.childreward.repository.TaskRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.support.CronExpression;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 核心调度服务，用于从任务模板自动生成任务实例。
 */
@Service
@Slf4j
public class TaskGenerationService {

    private final ScheduledTaskRepository scheduledTaskRepository;
    private final TaskRepository taskRepository;

    public TaskGenerationService(ScheduledTaskRepository scheduledTaskRepository, TaskRepository taskRepository) {
        this.scheduledTaskRepository = scheduledTaskRepository;
        this.taskRepository = taskRepository;
    }

    /**
     * 定时任务，用于从模板生成每日任务。
     * Cron表达式 "0 0 0 * * ?" 表示每天凌晨0点执行。
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void generateTasksFromTemplates() {
        log.info("开始执行计划任务生成...");
        List<ScheduledTask> activeTemplates = scheduledTaskRepository.findByActiveTrue();
        LocalDateTime now = LocalDateTime.now();

        for (ScheduledTask template : activeTemplates) {
            try {
                CronExpression cron = CronExpression.parse(template.getCronExpression());

                // 检查今天是否应该执行这个任务
                LocalDateTime todayStart = now.toLocalDate().atStartOfDay();
                LocalDateTime todayEnd = now.toLocalDate().atTime(23, 59, 59);

                // 检查从昨天23:59开始的下次执行时间，看是否在今天范围内
                LocalDateTime yesterdayEnd = todayStart.minusMinutes(1);
                LocalDateTime nextExecutionTime = cron.next(yesterdayEnd);

                log.info("检查模板ID: {} ('{}'), CRON: '{}', 下次执行时间: {}, 今天范围: {} - {}",
                    template.getId(), template.getTitle(), template.getCronExpression(), nextExecutionTime, todayStart, todayEnd);

                boolean shouldExecuteToday = nextExecutionTime != null &&
                    !nextExecutionTime.isBefore(todayStart) &&
                    !nextExecutionTime.isAfter(todayEnd);

                if (shouldExecuteToday) {
                    // 检查今天是否已经为这个模板生成过任务
                    if (taskRepository.existsBySourceTemplateIdAndScheduledDate(template.getId(), now.toLocalDate())) {
                        log.info("模板ID: {} ('{}') 今天已经生成过任务，跳过重复生成", template.getId(), template.getTitle());
                        continue;
                    }

                    log.info("检测到模板ID: {} ('{}') 今天需要执行，正在生成任务...", template.getId(), template.getTitle());
                    createTaskFromTemplate(template);
                } else {
                    log.info("模板ID: {} ('{}') 今天不需要执行，下次执行时间: {}",
                        template.getId(), template.getTitle(), nextExecutionTime);
                }
            } catch (IllegalArgumentException e) {
                log.error("模板ID: {} 的CRON表达式 '{}' 无效，已跳过。", template.getId(), template.getCronExpression(), e);
            }
        }
        log.info("计划任务生成结束。");
    }

    /**
     * 手动触发任务生成（用于测试）
     */
    public void manualGenerateTasksFromTemplates() {
        log.info("手动触发计划任务生成...");
        generateTasksFromTemplates();
    }

    /**
     * 根据任务模板创建一个具体的任务实例并保存到数据库。
     * @param template 任务模板
     */
    private void createTaskFromTemplate(ScheduledTask template) {
        LocalDate today = LocalDate.now();

        Task task = Task.builder()
                .sourceTemplateId(template.getId())
                .title(template.getTitle())
                .description(template.getDescription()) // 添加描述字段
                .taskType(determineTaskType(template))
                .basePoints(template.getPoints())
                .expectedMinutes(template.getExpectedMinutes() != null ? template.getExpectedMinutes() : 60) // 默认为60分钟
                .dueDate(today) // 设置为今天
                .dueTime(template.getDueTime() != null ? template.getDueTime() : LocalTime.of(23, 59, 59)) // 默认为当天午夜
                .scheduledDate(today) // 明确设置计划日期为今天
                .status(determineInitialStatus(template))
                .build();

        Task savedTask = taskRepository.save(task);
        log.info("成功为模板ID: {} ('{}') 生成了新的任务ID: {}, 计划日期: {}",
                template.getId(), template.getTitle(), savedTask.getId(), today);
    }

    /**
     * 根据任务模板确定任务类型。
     * @param template 任务模板
     * @return 任务类型
     */
    private TaskType determineTaskType(ScheduledTask template) {
        // 如果是惩罚审批类型，直接返回
        if (template.getType() == TaskType.PENALTY_APPROVAL) {
            return TaskType.PENALTY_APPROVAL;
        }

        // 根据任务要求类型设置任务类型
        if (template.getTaskRequirementType() == TaskRequirementType.REQUIRED) {
            return TaskType.REQUIRED;
        } else if (template.getTaskRequirementType() == TaskRequirementType.OPTIONAL) {
            return TaskType.OPTIONAL;
        }

        // 默认为必做任务
        return TaskType.REQUIRED;
    }

    /**
     * 根据任务模板确定初始状态。
     * @param template 任务模板
     * @return 初始状态
     */
    private TaskStatus determineInitialStatus(ScheduledTask template) {
        // 如果配置为直接进入审核，则状态为PENDING
        if (template.getDirectToReview() != null && template.getDirectToReview() == 1) {
            return TaskStatus.PENDING;
        }

        // 对于惩罚性任务，如果没有配置直接审核，也默认进入PENDING状态
        if (template.getType() == TaskType.PENALTY_APPROVAL) {
            return TaskStatus.PENDING;
        }

        // 对于其他任务，初始状态为NOT_STARTED
        return TaskStatus.NOT_STARTED;
    }
}