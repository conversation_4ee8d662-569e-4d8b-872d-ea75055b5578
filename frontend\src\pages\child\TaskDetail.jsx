import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { AppleDesignSystem } from '../../design/AppleDesignSystem';
import RichTextRenderer from '../../components/common/RichTextRenderer';
import { childApi } from '../../api/apiService';

// 全局样式来强制图片缩小
const GlobalStyle = styled.div`
  .task-detail-content img {
    max-width: 60% !important;
    width: 60% !important;
    height: auto !important;
    display: block !important;
    margin: 12px 0 !important;
  }
`;

const Container = styled.div`
  background: ${AppleDesignSystem.colors.semantic.systemBackground};
  padding: 0;
  position: relative;
  height: 100vh;
  overflow-y: scroll; /* 强制显示滚动条并启用滚动 */
  -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
`;

const Header = styled.div`
  position: sticky;
  top: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: ${AppleDesignSystem.spacing.md} ${AppleDesignSystem.spacing.lg};
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 100;
  display: flex;
  align-items: center;
  gap: ${AppleDesignSystem.spacing.md};
`;

const BackButton = styled(motion.button)`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  padding: ${AppleDesignSystem.spacing.sm};
  border-radius: ${AppleDesignSystem.borderRadius.medium};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${AppleDesignSystem.colors.semantic.label};
  
  &:hover {
    background: rgba(0, 0, 0, 0.05);
  }
`;

const HeaderTitle = styled.h1`
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: ${AppleDesignSystem.colors.semantic.label};
  flex: 1;
`;

const Content = styled.div`
  padding: ${AppleDesignSystem.spacing.lg};
  max-width: 100%;
  margin: 0;
  padding-bottom: 100px; /* 为底部导航栏留出空间 */
  display: flex;
  flex-direction: column;
`;

const TaskCard = styled(motion.div)`
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: ${AppleDesignSystem.borderRadius.large};
  padding: ${AppleDesignSystem.spacing.xl};
  display: flex;
  flex-direction: column;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  width: 100%;
`;

const TaskTitle = styled.h2`
  margin: 0 0 ${AppleDesignSystem.spacing.xl} 0;
  font-size: 32px;
  font-weight: 700;
  color: ${AppleDesignSystem.colors.semantic.label};
  text-align: center;
  line-height: 1.2;
`;

const InfoGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: ${AppleDesignSystem.spacing.lg};
  margin-bottom: ${AppleDesignSystem.spacing.xxl};

  @media (max-width: 600px) {
    grid-template-columns: 1fr;
  }
`;

const InfoItem = styled.div`
  text-align: center;
  padding: ${AppleDesignSystem.spacing.lg};
  background: rgba(0, 0, 0, 0.03);
  border-radius: ${AppleDesignSystem.borderRadius.large};
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
`;

const InfoLabel = styled.div`
  font-size: 16px;
  color: ${AppleDesignSystem.colors.semantic.secondaryLabel};
  margin-bottom: ${AppleDesignSystem.spacing.sm};
  font-weight: 500;
`;

const InfoValue = styled.div`
  font-size: 22px;
  font-weight: 700;
  color: ${AppleDesignSystem.colors.semantic.label};
`;

const DescriptionSection = styled.div`
  margin-bottom: ${AppleDesignSystem.spacing.xxl};
  min-height: 300px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 ${AppleDesignSystem.spacing.lg} 0;
  font-size: 24px;
  font-weight: 600;
  color: ${AppleDesignSystem.colors.semantic.label};
  display: flex;
  align-items: center;
  gap: ${AppleDesignSystem.spacing.sm};
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${AppleDesignSystem.spacing.lg};
  justify-content: center;
  margin-top: auto;
  padding-top: ${AppleDesignSystem.spacing.xl};
  padding-bottom: ${AppleDesignSystem.spacing.xl};
`;

const ActionButton = styled(motion.button)`
  padding: ${AppleDesignSystem.spacing.lg} ${AppleDesignSystem.spacing.xxl};
  border: none;
  border-radius: ${AppleDesignSystem.borderRadius.large};
  font-size: 18px;
  font-weight: 700;
  cursor: pointer;
  min-width: 160px;
  min-height: 56px;

  ${props => props.primary ? `
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  ` : `
    background: rgba(0, 0, 0, 0.05);
    color: ${AppleDesignSystem.colors.semantic.label};
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  `}

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.primary ?
      '0 6px 20px rgba(102, 126, 234, 0.4)' :
      '0 4px 12px rgba(0, 0, 0, 0.15)'
    };
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
  color: white;
  font-size: 18px;
`;

const TaskDetail = () => {
  const { taskId } = useParams();
  const navigate = useNavigate();
  const [task, setTask] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchTaskDetail();
  }, [taskId]);

  const fetchTaskDetail = async () => {
    try {
      setLoading(true);
      // 直接调用任务详情API获取完整数据（包含description字段）
      const response = await childApi.getTaskById(taskId);
      setTask(response.data);
    } catch (error) {
      console.error('获取任务详情失败:', error);
      // 如果详情API失败，尝试从今日任务列表中查找
      try {
        const response = await childApi.getTodayTasks();
        const tasks = response.data || [];
        const foundTask = tasks.find(t => t.id.toString() === taskId);
        setTask(foundTask);
      } catch (fallbackError) {
        console.error('备用方案也失败:', fallbackError);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleStartTask = async () => {
    try {
      await childApi.startTask(task.id);
      // 刷新任务数据
      await fetchTaskDetail();
    } catch (error) {
      console.error('开始任务失败:', error);
    }
  };

  const handleCompleteTask = async () => {
    try {
      await childApi.completeTask(task.id);
      // 刷新任务数据
      await fetchTaskDetail();
    } catch (error) {
      console.error('完成任务失败:', error);
    }
  };

  const getStatusLabel = (status) => {
    const statusMap = {
      'NOT_STARTED': '未开始',
      'IN_PROGRESS': '进行中',
      'COMPLETED': '已完成',
      'PENDING': '待审核',
      'REJECTED': '已作废', // 审批拒绝，质量不过关
      'OVERDUE': '已过期'   // 任务过期，没有完成
    };
    return statusMap[status] || status;
  };

  const getStatusColor = (status) => {
    const colorMap = {
      'NOT_STARTED': '#8E8E93',
      'IN_PROGRESS': '#007AFF',
      'COMPLETED': '#34C759',
      'PENDING': '#FF9500',
      'REJECTED': '#FF3B30', // 红色 - 已作废（审批拒绝）
      'OVERDUE': '#8E8E93'   // 灰色 - 已过期（任务过期）
    };
    return colorMap[status] || '#8E8E93';
  };

  if (loading) {
    return (
      <Container>
        <LoadingContainer>
          <div>加载中...</div>
        </LoadingContainer>
      </Container>
    );
  }

  if (!task) {
    return (
      <Container>
        <Header>
          <BackButton
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => navigate(-1)}
          >
            ←
          </BackButton>
          <HeaderTitle>任务详情</HeaderTitle>
        </Header>
        <LoadingContainer>
          <div>任务不存在</div>
        </LoadingContainer>
      </Container>
    );
  }

  return (
    <GlobalStyle>
      <Container>
      <Header>
        <BackButton
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => navigate(-1)}
        >
          ←
        </BackButton>
        <HeaderTitle>任务详情</HeaderTitle>
      </Header>

      <Content>
        <TaskCard
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <TaskTitle>{task.title}</TaskTitle>

          <InfoGrid>
            <InfoItem>
              <InfoLabel>可获积分</InfoLabel>
              <InfoValue>⭐ {task.basePoints}</InfoValue>
            </InfoItem>
            <InfoItem>
              <InfoLabel>预计用时</InfoLabel>
              <InfoValue>⏱️ {task.expectedMinutes}分钟</InfoValue>
            </InfoItem>
            <InfoItem>
              <InfoLabel>截止时间</InfoLabel>
              <InfoValue>🕒 {task.dueTime}</InfoValue>
            </InfoItem>
            <InfoItem>
              <InfoLabel>当前状态</InfoLabel>
              <InfoValue style={{ color: getStatusColor(task.status) }}>
                {getStatusLabel(task.status)}
              </InfoValue>
            </InfoItem>
          </InfoGrid>

          <DescriptionSection>
            <SectionTitle>
              <span>📋</span>
              <span>任务详情</span>
            </SectionTitle>
            <div style={{
              background: 'rgba(255, 255, 255, 0.5)',
              borderRadius: AppleDesignSystem.borderRadius.large,
              padding: AppleDesignSystem.spacing.xl,
              minHeight: '200px',
              border: '1px solid rgba(0, 0, 0, 0.05)'
            }} className="task-detail-content">
              {task.description ? (
                <RichTextRenderer
                  content={task.description}
                  maxHeight="none"
                  style={{
                    fontSize: '16px',
                    lineHeight: '1.6'
                  }}
                />
              ) : (
                <div style={{
                  color: '#999',
                  fontStyle: 'italic',
                  textAlign: 'center',
                  padding: '2rem'
                }}>
                  暂无任务详情描述
                  <br />
                  <small>Debug: description = "{task.description}"</small>
                </div>
              )}
            </div>
          </DescriptionSection>

          <ActionButtons>
            {task.status === 'NOT_STARTED' && (
              <ActionButton
                primary
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleStartTask}
              >
                开始任务
              </ActionButton>
            )}
            {task.status === 'IN_PROGRESS' && (
              <ActionButton
                primary
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleCompleteTask}
              >
                完成任务
              </ActionButton>
            )}
            <ActionButton
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => navigate(-1)}
            >
              返回
            </ActionButton>
          </ActionButtons>
        </TaskCard>
      </Content>
    </Container>
    </GlobalStyle>
  );
};

export default TaskDetail;
