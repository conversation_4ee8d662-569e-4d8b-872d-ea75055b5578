import React, { useState, useEffect } from 'react';
import { scheduledTaskApi } from '../api/scheduledTaskApi';
import ScheduledTaskForm from '../components/ScheduledTaskForm';
import RichTextRenderer from '../components/common/RichTextRenderer';
import Modal from '../components/common/Modal';
import styled from 'styled-components';

const PageContainer = styled.div`
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
`;

const PageHeader = styled.div`
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;

    h1 {
        color: #333;
        font-size: 2rem;
        margin: 0;
    }
`;

const AddButton = styled.button`
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }

    &:active {
        transform: translateY(0);
    }
`;

const SectionHeader = styled.h2`
    color: #555;
    margin: 2rem 0 1rem;
    font-size: 1.5rem;
`;

const TaskList = styled.div`
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
`;

const TaskCard = styled.div`
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    transition: transform 0.2s, box-shadow 0.2s;
    
    &:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
`;

const TaskHeader = styled.div`
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
`;

const TaskTitle = styled.h3`
    margin: 0;
    color: #333;
    font-size: 1.25rem;
`;

const TaskType = styled.span`
    display: inline-block;
    padding: 0.3rem 0.6rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    background-color: ${props => props.type === 'REWARD' ? 'rgba(46, 213, 115, 0.15)' : 'rgba(255, 71, 87, 0.15)'};
    color: ${props => props.type === 'REWARD' ? '#2ed573' : '#ff4757'};
`;

const TaskDetail = styled.div`
    margin-bottom: 0.5rem;
    color: #666;
    font-size: 0.9rem;
    
    strong {
        color: #444;
    }
`;

const ButtonGroup = styled.div`
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-top: 1.5rem;
`;

const Button = styled.button`
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: background-color 0.2s;
`;

const EditButton = styled(Button)`
    background-color: #f1f2f6;
    color: #333;
    
    &:hover {
        background-color: #dfe4ea;
    }
`;

const DeleteButton = styled(Button)`
    background-color: #ff4757;
    color: white;
    
    &:hover {
        background-color: #ee3651;
    }
`;

const LoadingMessage = styled.p`
    text-align: center;
    color: #666;
    font-size: 1.1rem;
    margin: 2rem 0;
`;

const ErrorMessage = styled.p`
    text-align: center;
    color: #ff4757;
    font-size: 1.1rem;
    margin: 2rem 0;
    padding: 1rem;
    background-color: rgba(255, 71, 87, 0.1);
    border-radius: 8px;
`;

const Divider = styled.hr`
    border: none;
    border-top: 1px solid #eee;
    margin: 2rem 0;
`;

const ActionBar = styled.div`
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
`;

const GenerateButton = styled(Button)`
    background-color: #007aff;
    color: white;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;

    &:hover {
        background-color: #0056cc;
    }

    &:disabled {
        background-color: #ccc;
        cursor: not-allowed;
    }
`;

// 不再使用mock数据，所有计划任务数据从API获取

const ScheduledTasksPage = () => {
    const [tasks, setTasks] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    const [editingTask, setEditingTask] = useState(null);
    const [isGenerating, setIsGenerating] = useState(false);
    const [showModal, setShowModal] = useState(false);
    const [isCreating, setIsCreating] = useState(false);

    const fetchTasks = async () => {
        try {
            setIsLoading(true);
            console.log('开始获取计划任务...');
            
            try {
                const response = await scheduledTaskApi.getAll();
                console.log('成功获取计划任务:', response);
                setTasks(response.data || []);
                setIsLoading(false);
            } catch (err) {
                setError('无法加载计划任务。');
                console.error('获取计划任务失败:', err);
                
                // 显示更详细的错误信息
                if (err.response) {
                    // 服务器返回了错误响应
                    console.error('错误响应状态:', err.response.status);
                    console.error('错误响应头:', err.response.headers);
                    console.error('错误响应数据:', err.response.data);
                } else if (err.request) {
                    // 请求已发送但没有收到响应
                    console.error('请求已发送但没有收到响应:', err.request);
                } else {
                    // 请求设置时发生错误
                    console.error('请求设置错误:', err.message);
                }
                
                // API调用失败时显示空列表
                console.log('API调用失败，显示空列表');
                setTasks([]);
                setIsLoading(false);
            }
        } catch (err) {
            setError('发生未知错误。');
            console.error('未知错误:', err);
            setTasks([]);
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchTasks();
    }, []);

    const handleFormSubmit = async (taskData) => {
        try {
            console.log('添加或更新计划:', taskData);
            
            if (editingTask) {
                // 更新现有计划
                const response = await scheduledTaskApi.update(editingTask.id, taskData);
                console.log('计划更新成功:', response);
            } else {
                // 创建新计划
                const response = await scheduledTaskApi.create(taskData);
                console.log('计划创建成功:', response);
            }

            // 关闭弹窗并刷新列表
            setShowModal(false);
            setEditingTask(null);
            setIsCreating(false);
            fetchTasks();
        } catch (err) {
            console.error("提交失败:", err);
            
            // 显示更详细的错误信息
            if (err.response) {
                console.error('错误响应状态:', err.response.status);
                console.error('错误响应数据:', err.response.data);
            }
            
            alert("操作失败！");
        }
    };

    const handleEdit = async (task) => {
        try {
            console.log('开始编辑任务，获取完整数据:', task.id);

            // 调用详情API获取完整数据（包含description字段）
            const response = await scheduledTaskApi.getById(task.id);
            const fullTaskData = response.data;

            // 对任务数据进行预处理，确保所有字段的格式正确
            const taskToEdit = {
                ...fullTaskData,
                // 确保dueTime是字符串格式
                dueTime: fullTaskData.dueTime ? String(fullTaskData.dueTime) : '09:00:00'
            };

            console.log('准备编辑任务（完整数据）:', taskToEdit);
            setEditingTask(taskToEdit);
            setIsCreating(false);
            setShowModal(true);
        } catch (err) {
            console.error('获取任务详情失败:', err);
            alert('获取任务详情失败，请重试');
        }
    };

    const handleAdd = () => {
        setEditingTask(null);
        setIsCreating(true);
        setShowModal(true);
    };

    const handleCancelEdit = () => {
        setEditingTask(null);
        setIsCreating(false);
        setShowModal(false);
    };

    const handleDelete = async (id) => {
        if (window.confirm('确定要删除这个计划吗？')) {
            try {
                console.log('删除计划:', id);
                await scheduledTaskApi.delete(id);
                console.log('计划删除成功');

                // 刷新计划列表
                fetchTasks();
            } catch (err) {
                console.error("删除失败:", err);

                // 显示更详细的错误信息
                if (err.response) {
                    console.error('错误响应状态:', err.response.status);
                    console.error('错误响应数据:', err.response.data);
                }

                alert("删除计划失败！");
            }
        }
    }

    const handleGenerateTasks = async () => {
        if (window.confirm('确定要立即生成今日任务吗？\n\n这将根据当前活跃的计划任务模板生成今日的任务实例。')) {
            setIsGenerating(true);
            try {
                console.log('开始生成任务...');
                const response = await scheduledTaskApi.generateTasks();
                console.log('任务生成成功:', response);

                alert('任务生成成功！\n\n已根据活跃的计划任务模板生成了今日的任务实例。');
            } catch (err) {
                console.error("任务生成失败:", err);

                // 显示更详细的错误信息
                let errorMessage = "任务生成失败！";
                if (err.response && err.response.data && err.response.data.message) {
                    errorMessage += `\n\n错误信息: ${err.response.data.message}`;
                }

                alert(errorMessage);
            } finally {
                setIsGenerating(false);
            }
        }
    }

    if (isLoading) return <LoadingMessage>加载中...</LoadingMessage>;
    if (error) return <ErrorMessage>{error}</ErrorMessage>;

    // 将CRON表达式转换为可读文本
    const getCronDescription = (cronExpression) => {
        if (cronExpression.includes('* * ?')) {
            const [_, minute, hour] = cronExpression.split(' ');
            return `每天 ${hour}:${minute}`;
        } else if (cronExpression.includes('? *')) {
            const [_, minute, hour, __, ___, days] = cronExpression.split(' ');
            const dayNames = {
                'MON': '周一',
                'TUE': '周二',
                'WED': '周三',
                'THU': '周四',
                'FRI': '周五',
                'SAT': '周六',
                'SUN': '周日'
            };
            const daysList = days.split(',').map(d => dayNames[d] || d).join('、');
            return `${daysList} ${hour}:${minute}`;
        }
        return cronExpression;
    };

    return (
        <PageContainer>
            <PageHeader>
                <h1>计划</h1>
                <AddButton onClick={handleAdd}>
                    + 新增计划
                </AddButton>
            </PageHeader>

            <Modal
                isOpen={showModal}
                onClose={handleCancelEdit}
                closeOnOverlayClick={false}
            >
                <ScheduledTaskForm
                    onSubmit={handleFormSubmit}
                    initialData={editingTask}
                    onCancel={handleCancelEdit}
                />
            </Modal>

            <ActionBar>
                <SectionHeader style={{ margin: 0 }}>已有的计划</SectionHeader>
                <GenerateButton
                    onClick={handleGenerateTasks}
                    disabled={isGenerating}
                >
                    {isGenerating ? '生成中...' : '立即生成任务'}
                </GenerateButton>
            </ActionBar>
            <TaskList>
                {tasks.map(task => (
                    <TaskCard key={task.id}>
                        <TaskHeader>
                            <TaskTitle>{task.title}</TaskTitle>
                            <TaskType type={task.type}>
                                {task.type === 'REWARD' ? '奖励审批' : '惩罚审批'}
                            </TaskType>
                        </TaskHeader>
                        
                        {task.description && (
                            <TaskDetail>
                                <RichTextRenderer content={task.description} maxHeight="200px" />
                            </TaskDetail>
                        )}
                        
                        <TaskDetail>
                            <strong>分值:</strong> {task.points} 分
                        </TaskDetail>
                        
                        <TaskDetail>
                            <strong>执行时间:</strong> {getCronDescription(task.cronExpression)}
                        </TaskDetail>

                        <TaskDetail>
                            <strong>状态:</strong>
                            <span style={{
                                color: task.active ? '#4cd964' : '#ff9500',
                                fontWeight: 'bold',
                                marginLeft: '8px'
                            }}>
                                {task.active ? '✓ 已启用' : '○ 已暂停'}
                            </span>
                        </TaskDetail>

                        <TaskDetail>
                            <strong>任务类型:</strong>
                            <span style={{
                                color: task.taskRequirementType === 'REQUIRED' ? '#ff3b30' : '#007aff',
                                fontWeight: 'bold',
                                marginLeft: '8px'
                            }}>
                                {task.taskRequirementType === 'REQUIRED' ? '必做任务' : '选做任务'}
                            </span>
                        </TaskDetail>

                        <TaskDetail>
                            <strong>审批类型:</strong> {task.type === 'REWARD' ? '完成后加分审批' : '触发后扣分审批'}
                        </TaskDetail>

                        {task.expectedMinutes > 0 && (
                            <TaskDetail>
                                <strong>预计用时:</strong> {task.expectedMinutes} 分钟
                            </TaskDetail>
                        )}
                        
                        <ButtonGroup>
                            <EditButton onClick={() => handleEdit(task)}>
                                编辑
                            </EditButton>
                            <DeleteButton onClick={() => handleDelete(task.id)}>
                                删除
                            </DeleteButton>
                        </ButtonGroup>
                    </TaskCard>
                ))}
            </TaskList>
        </PageContainer>
    );
};

export default ScheduledTasksPage; 